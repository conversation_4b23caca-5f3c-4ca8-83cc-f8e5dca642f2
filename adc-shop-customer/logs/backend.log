Running customer-api...
{"level":"info","msg":"Starting Customer API","time":"2025-06-05T12:41:04+07:00"}
{"level":"info","msg":"Using DATABASE_URL for database connection","time":"2025-06-05T12:41:04+07:00"}
{"level":"info","msg":"Database connection established","time":"2025-06-05T12:41:04+07:00"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /health                   --> customer-backend/internal/api/routes.SetupRoutes.func1 (6 handlers)
[GIN-debug] GET    /shops                    --> customer-backend/internal/api/handlers.(*ShopHandler).GetShops-fm (6 handlers)
[GIN-debug] GET    /shops/search             --> customer-backend/internal/api/handlers.(*ShopHandler).SearchShops-fm (6 handlers)
[GIN-debug] GET    /shops/popular            --> customer-backend/internal/api/handlers.(*ShopHandler).GetPopularShops-fm (6 handlers)
[GIN-debug] GET    /shops/nearby             --> customer-backend/internal/api/handlers.(*ShopHandler).GetNearbyShops-fm (6 handlers)
[GIN-debug] GET    /shops/category/:category --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/filter-options     --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopFilterOptions-fm (6 handlers)
[GIN-debug] GET    /shops/:id                --> customer-backend/internal/api/handlers.(*ShopHandler).GetShop-fm (6 handlers)
[GIN-debug] GET    /shops/:id/status         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatus-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu           --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/popular   --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/new       --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/search    --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItems-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategories-fm (6 handlers)
[GIN-debug] GET    /shops/:id/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategory-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug         --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/status  --> customer-backend/internal/api/handlers.(*ShopHandler).GetShopStatusBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu    --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryBySlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/popular --> customer-backend/internal/api/handlers.(*MenuHandler).GetPopularItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/new --> customer-backend/internal/api/handlers.(*MenuHandler).GetNewItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/vegetarian --> customer-backend/internal/api/handlers.(*MenuHandler).GetVegetarianItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/search --> customer-backend/internal/api/handlers.(*MenuHandler).SearchMenuItemsByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuCategoriesByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/menu/categories/:categoryId --> customer-backend/internal/api/handlers.(*MenuHandler).GetItemsByCategoryByBranchSlug-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables --> customer-backend/internal/api/handlers.(*TableHandler).GetTablesByBranch-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/available --> customer-backend/internal/api/handlers.(*TableHandler).GetAvailableTablesByBranch-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/number/:tableNumber --> customer-backend/internal/api/handlers.(*TableHandler).GetTableByNumber-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/tables/:tableId/validate --> customer-backend/internal/api/handlers.(*TableHandler).ValidateTableForOrder-fm (6 handlers)
[GIN-debug] GET    /shops/slug/:slug/branches/slug/:branchSlug/table-areas --> customer-backend/internal/api/handlers.(*TableHandler).GetTableAreas-fm (6 handlers)
[GIN-debug] GET    /menu/items/:itemId       --> customer-backend/internal/api/handlers.(*MenuHandler).GetMenuItem-fm (6 handlers)
[GIN-debug] GET    /tables/:tableId          --> customer-backend/internal/api/handlers.(*TableHandler).GetTableByID-fm (6 handlers)
[GIN-debug] POST   /orders/table             --> customer-backend/internal/api/handlers.(*OrderHandler).CreateTableOrder-fm (6 handlers)
[GIN-debug] GET    /orders/:orderId          --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrderByID-fm (6 handlers)
[GIN-debug] GET    /orders/number/:orderNumber --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrderByNumber-fm (6 handlers)
[GIN-debug] GET    /orders/table/:tableId    --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrdersByTable-fm (6 handlers)
[GIN-debug] GET    /orders/table/:tableId/active --> customer-backend/internal/api/handlers.(*OrderHandler).GetActiveOrdersByTable-fm (6 handlers)
[GIN-debug] GET    /orders/customer          --> customer-backend/internal/api/handlers.(*OrderHandler).GetOrdersByCustomer-fm (6 handlers)
[GIN-debug] PUT    /orders/:orderId/status   --> customer-backend/internal/api/handlers.(*OrderHandler).UpdateOrderStatus-fm (6 handlers)
[GIN-debug] POST   /payments/create-intent   --> customer-backend/internal/api/handlers.(*PaymentHandler).CreatePaymentIntent-fm (6 handlers)
[GIN-debug] POST   /payments/confirm         --> customer-backend/internal/api/handlers.(*PaymentHandler).ConfirmPayment-fm (6 handlers)
[GIN-debug] GET    /payments/:paymentIntentId/status --> customer-backend/internal/api/handlers.(*PaymentHandler).GetPaymentStatus-fm (6 handlers)
[GIN-debug] POST   /payments/:paymentIntentId/cancel --> customer-backend/internal/api/handlers.(*PaymentHandler).CancelPayment-fm (6 handlers)
[GIN-debug] POST   /payments/webhook         --> customer-backend/internal/api/handlers.(*PaymentHandler).HandleWebhook-fm (6 handlers)
[GIN-debug] GET    /payments/config          --> customer-backend/internal/api/handlers.(*PaymentHandler).GetPublishableKey-fm (6 handlers)
[GIN-debug] GET    /cart                     --> customer-backend/internal/api/handlers.(*CartHandler).GetCart-fm (6 handlers)
[GIN-debug] POST   /cart/add                 --> customer-backend/internal/api/handlers.(*CartHandler).AddToCart-fm (6 handlers)
[GIN-debug] PUT    /cart/update              --> customer-backend/internal/api/handlers.(*CartHandler).UpdateQuantity-fm (6 handlers)
[GIN-debug] DELETE /cart/remove              --> customer-backend/internal/api/handlers.(*CartHandler).RemoveFromCart-fm (6 handlers)
[GIN-debug] DELETE /cart/clear               --> customer-backend/internal/api/handlers.(*CartHandler).ClearCart-fm (6 handlers)
[GIN-debug] DELETE /cart/clear-branch        --> customer-backend/internal/api/handlers.(*CartHandler).ClearBranchCart-fm (6 handlers)
[GIN-debug] POST   /cart/sync                --> customer-backend/internal/api/handlers.(*CartHandler).SyncCartOnLogin-fm (6 handlers)
[GIN-debug] GET    /docs/*any                --> customer-backend/internal/api/routes.SetupRoutes.func2 (6 handlers)
{"level":"info","msg":"Customer API starting on port 8900","time":"2025-06-05T12:41:04+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: thai-delight, branch slug: downtown, page=1, limit=20","time":"2025-06-05T12:41:13+07:00"}

2025/06/05 12:41:13 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [35;1mrecord not found
[0m[33m[121.355ms] [34;1m[rows:0][0m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_9ac85c8f-40c8-4549-84ed-a43ca1f4f757' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1

2025/06/05 12:41:13 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:495 [33mSLOW SQL >= 200ms
[0m[31;1m[330.097ms] [33m[rows:1][35m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."latitude","shop_branches"."longitude","shop_branches"."location_accuracy","shop_branches"."geocoded_at","shop_branches"."place_id","shop_branches"."formatted_address","shop_branches"."location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'thai-delight' AND shop_branches.slug = 'downtown' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1[0m
{"level":"info","msg":"Looking for branch with shop slug: thai-delight, branch slug: downtown","time":"2025-06-05T12:41:13+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:41:13 +07] \"GET /cart HTTP/1.1 200 455.985459ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:41:13+07:00"}

2025/06/05 12:41:14 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:359 [33mSLOW SQL >= 200ms
[0m[31;1m[214.388ms] [33m[rows:3][35m SELECT 
			menu_items.id,
			menu_items.name,
			menu_items.description,
			menu_items.price,
			menu_items.images,
			menu_items.is_available,
			menu_items.is_vegetarian,
			menu_items.is_vegan,
			menu_items.is_gluten_free,
			menu_items.is_spicy,
			menu_items.spice_level,
			menu_items.preparation_time,
			menu_items.tags,
			menu_items.allergens,
			menu_items.category_id,
			COALESCE(menu_categories.name, 'Uncategorized') as category_name,
			menu_items.created_at,
			menu_items.updated_at
		 FROM "menu_items" LEFT JOIN menu_categories ON menu_items.category_id = menu_categories.id WHERE (menu_items.branch_id = '550e8400-e29b-41d4-a716-446655440101' AND menu_items.is_available = true) AND is_available = true ORDER BY menu_items.created_at DESC LIMIT 20[0m
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:41:14 +07] \"GET /shops/slug/thai-delight/branches/slug/downtown/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 200 1.260262333s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:41:14+07:00"}

2025/06/05 12:41:17 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [35;1mrecord not found
[0m[33m[128.811ms] [34;1m[rows:0][0m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_fdcfb000-0337-4787-9468-309ef6957bdf' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1

2025/06/05 12:41:18 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:99 [35;1mrecord not found
[0m[33m[91.750ms] [34;1m[rows:0][0m SELECT * FROM "cart_items" WHERE (cart_session_id = '473cc445-ec8c-40db-ac51-816fd66fdc18' AND menu_item_id = '94f2bbfe-8f11-4e63-8f93-c0307a2c1935' AND shop_slug = 'thai-delight' AND branch_slug = 'downtown') AND "cart_items"."deleted_at" IS NULL ORDER BY "cart_items"."id" LIMIT 1
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:41:18 +07] \"POST /cart/add HTTP/1.1 200 939.070917ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:41:18+07:00"}

2025/06/05 12:41:20 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [35;1mrecord not found
[0m[33m[84.937ms] [34;1m[rows:0][0m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_261bbbe6-ac63-4577-853b-bbf18f2719ba' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:41:20 +07] \"GET /cart HTTP/1.1 200 301.831792ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:41:20+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: thai-delight, branch slug: downtown, page=1, limit=20","time":"2025-06-05T12:41:24+07:00"}

2025/06/05 12:41:24 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [35;1mrecord not found
[0m[33m[78.263ms] [34;1m[rows:0][0m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_b69dec55-b4b6-4679-8f9d-b35e459c51b4' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1
{"level":"info","msg":"Looking for branch with shop slug: thai-delight, branch slug: downtown","time":"2025-06-05T12:41:24+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:41:24 +07] \"GET /cart HTTP/1.1 200 286.39925ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:41:24+07:00"}

2025/06/05 12:41:24 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/menu_repository.go:359 [33mSLOW SQL >= 200ms
[0m[31;1m[223.236ms] [33m[rows:3][35m SELECT 
			menu_items.id,
			menu_items.name,
			menu_items.description,
			menu_items.price,
			menu_items.images,
			menu_items.is_available,
			menu_items.is_vegetarian,
			menu_items.is_vegan,
			menu_items.is_gluten_free,
			menu_items.is_spicy,
			menu_items.spice_level,
			menu_items.preparation_time,
			menu_items.tags,
			menu_items.allergens,
			menu_items.category_id,
			COALESCE(menu_categories.name, 'Uncategorized') as category_name,
			menu_items.created_at,
			menu_items.updated_at
		 FROM "menu_items" LEFT JOIN menu_categories ON menu_items.category_id = menu_categories.id WHERE (menu_items.branch_id = '550e8400-e29b-41d4-a716-446655440101' AND menu_items.is_available = true) AND is_available = true ORDER BY menu_items.created_at DESC LIMIT 20[0m
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:41:25 +07] \"GET /shops/slug/thai-delight/branches/slug/downtown/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 200 1.020021084s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:41:25+07:00"}

2025/06/05 12:41:25 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [35;1mrecord not found
[0m[33m[41.892ms] [34;1m[rows:0][0m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_7dc2adea-8de7-43de-a328-08a1e0cbb9ae' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1
{"level":"info","msg":"Getting menu items for shop slug: thai-delight, branch slug: downtown, page=1, limit=20","time":"2025-06-05T12:41:25+07:00"}
{"level":"info","msg":"Looking for branch with shop slug: thai-delight, branch slug: downtown","time":"2025-06-05T12:41:25+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:41:26 +07] \"GET /cart HTTP/1.1 200 238.064167ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:41:26+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:41:26 +07] \"GET /shops/slug/thai-delight/branches/slug/downtown/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 200 710.448542ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:41:26+07:00"}

2025/06/05 12:41:34 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [35;1mrecord not found
[0m[33m[83.604ms] [34;1m[rows:0][0m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_67649ebb-1048-49f1-99ae-e30623a80d6a' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1

2025/06/05 12:41:34 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:99 [35;1mrecord not found
[0m[33m[40.299ms] [34;1m[rows:0][0m SELECT * FROM "cart_items" WHERE (cart_session_id = 'fb588e1a-7235-408b-933e-c47fad2672ef' AND menu_item_id = '0f98f70c-5bb2-4a6b-b731-3a60208a3475' AND shop_slug = 'thai-delight' AND branch_slug = 'downtown') AND "cart_items"."deleted_at" IS NULL ORDER BY "cart_items"."id" LIMIT 1

2025/06/05 12:41:34 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:105 [35;1mERROR: value too long for type character varying(500) (SQLSTATE 22001)
[0m[33m[172.333ms] [34;1m[rows:0][0m INSERT INTO "cart_items" ("cart_session_id","menu_item_id","quantity","name","description","image","price","shop_slug","branch_slug","table_id","table_number","shop_id","branch_id","created_at","updated_at","deleted_at") VALUES ('fb588e1a-7235-408b-933e-c47fad2672ef','0f98f70c-5bb2-4a6b-b731-3a60208a3475',1,'Test Restaurant','ccc','data:image/jpeg;base64,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',44.77,'thai-delight','downtown',NULL,NULL,NULL,NULL,'2025-06-05 12:41:34.324','2025-06-05 12:41:34.324',NULL) RETURNING "id"
{"error":"ERROR: value too long for type character varying(500) (SQLSTATE 22001)","level":"error","msg":"Failed to create cart item","time":"2025-06-05T12:41:34+07:00"}
{"error":"failed to create cart item: ERROR: value too long for type character varying(500) (SQLSTATE 22001)","level":"error","msg":"Failed to add item to cart","time":"2025-06-05T12:41:34+07:00"}
{"error":"failed to create cart item: ERROR: value too long for type character varying(500) (SQLSTATE 22001)","level":"error","msg":"Failed to add item to cart","time":"2025-06-05T12:41:34+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:41:34 +07] \"POST /cart/add HTTP/1.1 500 518.921ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:41:34+07:00"}

2025/06/05 12:41:41 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [35;1mrecord not found
[0m[33m[240.010ms] [34;1m[rows:0][0m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_0d4e2011-238a-4331-8865-c46a418eacd8' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1

2025/06/05 12:41:41 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:48 [33mSLOW SQL >= 200ms
[0m[31;1m[318.133ms] [33m[rows:1][35m INSERT INTO "cart_sessions" ("user_id","session_id","expires_at","created_at","updated_at","deleted_at") VALUES (NULL,'guest_0d4e2011-238a-4331-8865-c46a418eacd8','2025-06-06 12:41:41.436','2025-06-05 12:41:41.575','2025-06-05 12:41:41.575',NULL) RETURNING "id"[0m

2025/06/05 12:41:41 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:55 [33mSLOW SQL >= 200ms
[0m[31;1m[212.897ms] [33m[rows:1][35m SELECT * FROM "cart_sessions" WHERE "cart_sessions"."id" = 'ae06e9d7-3ef9-4e63-bc24-e99de6d2ec2f' AND "cart_sessions"."deleted_at" IS NULL AND "cart_sessions"."id" = 'ae06e9d7-3ef9-4e63-bc24-e99de6d2ec2f' ORDER BY "cart_sessions"."id" LIMIT 1[0m

2025/06/05 12:41:42 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:99 [35;1mrecord not found
[0m[33m[149.679ms] [34;1m[rows:0][0m SELECT * FROM "cart_items" WHERE (cart_session_id = 'ae06e9d7-3ef9-4e63-bc24-e99de6d2ec2f' AND menu_item_id = '94f2bbfe-8f11-4e63-8f93-c0307a2c1935' AND shop_slug = 'thai-delight' AND branch_slug = 'downtown') AND "cart_items"."deleted_at" IS NULL ORDER BY "cart_items"."id" LIMIT 1

2025/06/05 12:41:42 [32m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:105 [33mSLOW SQL >= 200ms
[0m[31;1m[295.925ms] [33m[rows:1][35m INSERT INTO "cart_items" ("cart_session_id","menu_item_id","quantity","name","description","image","price","shop_slug","branch_slug","table_id","table_number","shop_id","branch_id","created_at","updated_at","deleted_at") VALUES ('ae06e9d7-3ef9-4e63-bc24-e99de6d2ec2f','94f2bbfe-8f11-4e63-8f93-c0307a2c1935',1,'ข้าวแกงเขียวหวาน','This dish, also known as Thai Green Curry, is a delightful blend of creamy coconut milk, tender chicken, and fresh vegetables. The curry paste is made from a complex blend of herbs and spices, giving the dish its characteristic rich flavor and vibrant green color.','https://oaidalleapiprodscus.blob.core.windows.net/private/org-TaXGfZxU9GbLQGz0gwzOgsSF/user-k3X3tLQr4hvhyz0gsXG9A15I/img-1P89yxXS3N8EgdOHXg7e0VrE.png?st=2025-06-02T03%3A53%3A25Z&se=2025-06-02T05%3A53%3A25Z&sp=r&sv=2024-08-04&sr=b&rscd=inline&rsct=image/png&skoid=475fd488-6c59-44a5-9aa9-31c4db451bea&sktid=a48cca56-e6da-484e-a814-9c849652bcb3&skt=2025-06-02T01%3A45%3A38Z&ske=2025-06-03T01%3A45%3A38Z&sks=b&skv=2024-08-04&sig=17XCJXdxmYG%2BVgoytnEl2KwOr1APmFxoF5IGTyHMR5k%3D',15,'thai-delight','downtown',NULL,NULL,NULL,NULL,'2025-06-05 12:41:42.286','2025-06-05 12:41:42.286',NULL) RETURNING "id"[0m
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:41:42 +07] \"POST /cart/add HTTP/1.1 200 1.422376333s \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:41:42+07:00"}

2025/06/05 12:41:50 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:38 [35;1mrecord not found
[0m[33m[77.549ms] [34;1m[rows:0][0m SELECT * FROM "cart_sessions" WHERE (session_id = 'guest_7db63b6c-2a14-4f71-a51c-b4696513abd1' AND user_id IS NULL) AND "cart_sessions"."deleted_at" IS NULL ORDER BY "cart_sessions"."id" LIMIT 1

2025/06/05 12:41:50 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:99 [35;1mrecord not found
[0m[33m[39.566ms] [34;1m[rows:0][0m SELECT * FROM "cart_items" WHERE (cart_session_id = '80e5c5cb-20e4-4921-b4b3-2a9928e38d6f' AND menu_item_id = 'd9c35d76-5a8d-48d1-96f1-9d5cb74dcf92' AND shop_slug = 'thai-delight' AND branch_slug = 'downtown') AND "cart_items"."deleted_at" IS NULL ORDER BY "cart_items"."id" LIMIT 1

2025/06/05 12:41:51 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/cart_repository.go:105 [35;1mERROR: value too long for type character varying(500) (SQLSTATE 22001)
[0m[33m[161.627ms] [34;1m[rows:0][0m INSERT INTO "cart_items" ("cart_session_id","menu_item_id","quantity","name","description","image","price","shop_slug","branch_slug","table_id","table_number","shop_id","branch_id","created_at","updated_at","deleted_at") VALUES ('80e5c5cb-20e4-4921-b4b3-2a9928e38d6f','d9c35d76-5a8d-48d1-96f1-9d5cb74dcf92',1,'Test Menu Item','This is a test item','data:image/jpeg;base64,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',15.99,'thai-delight','downtown',NULL,NULL,NULL,NULL,'2025-06-05 12:41:51.013','2025-06-05 12:41:51.013',NULL) RETURNING "id"
{"error":"ERROR: value too long for type character varying(500) (SQLSTATE 22001)","level":"error","msg":"Failed to create cart item","time":"2025-06-05T12:41:51+07:00"}
{"error":"failed to create cart item: ERROR: value too long for type character varying(500) (SQLSTATE 22001)","level":"error","msg":"Failed to add item to cart","time":"2025-06-05T12:41:51+07:00"}
{"error":"failed to create cart item: ERROR: value too long for type character varying(500) (SQLSTATE 22001)","level":"error","msg":"Failed to add item to cart","time":"2025-06-05T12:41:51+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:41:51 +07] \"POST /cart/add HTTP/1.1 500 480.681834ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:41:51+07:00"}
{"all_query_params":{"limit":["20"],"page":["1"]},"level":"info","msg":"Received query parameters","time":"2025-06-05T12:43:19+07:00"}
{"cuisine_type_after_bind":null,"level":"info","min_rating_after_bind":null,"msg":"Filters after ShouldBindQuery","price_range_after_bind":null,"search_after_bind":"","time":"2025-06-05T12:43:19+07:00"}
{"cuisine_type":null,"level":"info","limit":20,"min_rating":null,"msg":"Processing shop filters","page":1,"price_range":null,"query_params":"page=1\u0026limit=20","search":"","time":"2025-06-05T12:43:19+07:00"}
{"level":"info","msg":"Getting shops with filters: page=1, limit=20","time":"2025-06-05T12:43:19+07:00"}
{"cuisine_type":null,"level":"info","min_rating":null,"msg":"Applying shop filters to query","price_range":null,"search":"","time":"2025-06-05T12:43:19+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:43:19 +07] \"GET /shops?page=1\u0026limit=20 HTTP/1.1 200 210.351375ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:43:19+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:43:19 +07] \"GET /shops/filter-options HTTP/1.1 200 241.56275ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:43:19+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: weerawat-poseeya, branch slug: downtown, page=1, limit=20","time":"2025-06-05T12:43:22+07:00"}

2025/06/05 12:43:22 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:495 [35;1mrecord not found
[0m[33m[84.355ms] [34;1m[rows:0][0m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."latitude","shop_branches"."longitude","shop_branches"."location_accuracy","shop_branches"."geocoded_at","shop_branches"."place_id","shop_branches"."formatted_address","shop_branches"."location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'weerawat-poseeya' AND shop_branches.slug = 'downtown' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1
{"error":"branch not found or not accessible: branch not found","level":"error","msg":"Failed to get menu items by branch slug","time":"2025-06-05T12:43:22+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:43:22 +07] \"GET /shops/slug/weerawat-poseeya/branches/slug/downtown/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 500 84.575083ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:43:22+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: weerawat-poseeya, branch slug: downtown, page=1, limit=20","time":"2025-06-05T12:44:09+07:00"}

2025/06/05 12:44:10 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:495 [35;1mrecord not found
[0m[33m[82.952ms] [34;1m[rows:0][0m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."latitude","shop_branches"."longitude","shop_branches"."location_accuracy","shop_branches"."geocoded_at","shop_branches"."place_id","shop_branches"."formatted_address","shop_branches"."location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'weerawat-poseeya' AND shop_branches.slug = 'downtown' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1
{"error":"branch not found or not accessible: branch not found","level":"error","msg":"Failed to get menu items by branch slug","time":"2025-06-05T12:44:10+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:44:10 +07] \"GET /shops/slug/weerawat-poseeya/branches/slug/downtown/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 500 83.463458ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:44:10+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: weerawat-poseeya, branch slug: downtown, page=1, limit=20","time":"2025-06-05T12:44:16+07:00"}

2025/06/05 12:44:16 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:495 [35;1mrecord not found
[0m[33m[78.457ms] [34;1m[rows:0][0m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."latitude","shop_branches"."longitude","shop_branches"."location_accuracy","shop_branches"."geocoded_at","shop_branches"."place_id","shop_branches"."formatted_address","shop_branches"."location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'weerawat-poseeya' AND shop_branches.slug = 'downtown' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1
{"error":"branch not found or not accessible: branch not found","level":"error","msg":"Failed to get menu items by branch slug","time":"2025-06-05T12:44:16+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:44:16 +07] \"GET /shops/slug/weerawat-poseeya/branches/slug/downtown/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 500 78.681666ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:44:16+07:00"}
{"level":"info","msg":"Getting menu items for shop slug: weerawat-poseeya, branch slug: downtown, page=1, limit=20","time":"2025-06-05T12:44:22+07:00"}

2025/06/05 12:44:22 [31;1m/Users/<USER>/Desktop/adc/adc-shop/adc-shop-customer/customer-backend/internal/repositories/shop_repository.go:495 [35;1mrecord not found
[0m[33m[79.259ms] [34;1m[rows:0][0m SELECT "shop_branches"."id","shop_branches"."created_at","shop_branches"."updated_at","shop_branches"."deleted_at","shop_branches"."shop_id","shop_branches"."name","shop_branches"."slug","shop_branches"."email","shop_branches"."phone","shop_branches"."address_street","shop_branches"."address_city","shop_branches"."address_state","shop_branches"."address_zip_code","shop_branches"."address_country","shop_branches"."latitude","shop_branches"."longitude","shop_branches"."location_accuracy","shop_branches"."geocoded_at","shop_branches"."place_id","shop_branches"."formatted_address","shop_branches"."location_type","shop_branches"."settings","shop_branches"."business_hours","shop_branches"."timezone","shop_branches"."status","shop_branches"."is_active" FROM "shop_branches" JOIN shops ON shop_branches.shop_id = shops.id WHERE (shops.slug = 'weerawat-poseeya' AND shop_branches.slug = 'downtown' AND shops.is_active = true AND shop_branches.is_active = true) AND "shop_branches"."deleted_at" IS NULL ORDER BY "shop_branches"."id" LIMIT 1
{"error":"branch not found or not accessible: branch not found","level":"error","msg":"Failed to get menu items by branch slug","time":"2025-06-05T12:44:22+07:00"}
{"level":"info","msg":"::1 - [Thu, 05 Jun 2025 12:44:22 +07] \"GET /shops/slug/weerawat-poseeya/branches/slug/downtown/menu?is_available=true\u0026page=1\u0026limit=20 HTTP/1.1 500 79.685083ms \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\" \"\n","time":"2025-06-05T12:44:22+07:00"}
signal: killed
make[2]: *** [run] Error 1
